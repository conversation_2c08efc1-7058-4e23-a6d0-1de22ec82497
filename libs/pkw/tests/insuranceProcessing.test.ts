import { mock, test } from 'node:test';
import assert from 'node:assert';
import {
    calculateInsuranceAmount,
    LOW_OUTS_COUNT,
    MEDIUM_OUTS_COUNT,
    HIGH_OUTS_COUNT,
    SERVER_COVERAGE_AMOUNT,
} from '../src/pkw_ts/tools/insuranceProcessing';
import { weightedRandomSelection } from 'shared/src/strategy';

// Mock the logging module to avoid dependencies
mock.method(console, 'log', mock.fn());

test('shouldBuyInsurance probability increases with higher outsCount', async () => {
    const testCases = [
        { outsCount: 1, expectedProbability: 0.1 },
        { outsCount: LOW_OUTS_COUNT, expectedProbability: 0.1 },
        { outsCount: 5, expectedProbability: 0.25 },
        { outsCount: MEDIUM_OUTS_COUNT, expectedProbability: 0.25 },
        { outsCount: 10, expectedProbability: 0.35 },
        { outsCount: HIGH_OUTS_COUNT, expectedProbability: 0.35 },
        { outsCount: 15, expectedProbability: 0.55 },
        { outsCount: 20, expectedProbability: 0.55 },
    ];

    const originalRandom = Math.random;

    for (const testCase of testCases) {
        // Test with random value just below probability (should buy)
        Math.random = () => testCase.expectedProbability - 0.01;
        let result = calculateInsuranceAmount(testCase.outsCount);
        assert.ok(
            result !== 0 && typeof result === 'number',
            `Expected to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );

        // Test with random value just above probability (should not buy)
        Math.random = () => testCase.expectedProbability + 0.01;
        result = calculateInsuranceAmount(testCase.outsCount);
        assert.strictEqual(
            result,
            null,
            `Expected NOT to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - LOW outsCount should prefer smaller coverage amounts', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    // Force buying insurance and collect coverage amounts
    Math.random = () => 0; // Always buy insurance

    // Run multiple times to test distribution
    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(LOW_OUTS_COUNT);
        if (result !== null) {
            results.push(result);
        }
    }

    // Verify that only expected coverage amounts are returned for LOW outsCount
    const expectedAmounts = [
        SERVER_COVERAGE_AMOUNT.LOW,
        SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
        SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
    ];
    for (const amount of results) {
        assert.ok(expectedAmounts.includes(amount), `Unexpected coverage amount ${amount} for LOW outsCount`);
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - MEDIUM outsCount should include balanced coverage', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(MEDIUM_OUTS_COUNT);
        if (result !== null) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        SERVER_COVERAGE_AMOUNT.LOW,
        SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
        SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
        SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for MEDIUM outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - HIGH outsCount should prefer medium to high coverage', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(HIGH_OUTS_COUNT);
        if (result !== null) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
        SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
        SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
        SERVER_COVERAGE_AMOUNT.FULL_POT,
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('Edge cases - boundary values for outsCount', async () => {
    const originalRandom = Math.random;

    // Test exact boundary values
    const boundaryTests = [
        { outsCount: 0, description: 'zero outsCount' },
        { outsCount: LOW_OUTS_COUNT, description: 'exactly LOW threshold' },
        { outsCount: LOW_OUTS_COUNT + 1, description: 'just above LOW threshold' },
        { outsCount: MEDIUM_OUTS_COUNT, description: 'exactly MEDIUM threshold' },
        { outsCount: MEDIUM_OUTS_COUNT + 1, description: 'just above MEDIUM threshold' },
        { outsCount: HIGH_OUTS_COUNT, description: 'exactly HIGH threshold' },
        { outsCount: HIGH_OUTS_COUNT + 1, description: 'just above HIGH threshold' },
        { outsCount: 100, description: 'very large outsCount' },
    ];

    for (const test of boundaryTests) {
        // Test that function doesn't throw errors
        Math.random = () => 0; // Always buy
        const result = calculateInsuranceAmount(test.outsCount);
        assert.ok(
            result === null || typeof result === 'number',
            `Should return null or number for ${test.description}`,
        );

        if (result !== null) {
            assert.ok(
                typeof result === 'number',
                `Should have numeric amount when buying for ${test.description}`,
            );
            assert.ok(result > 0, `Amount should be positive for ${test.description}`);
        }
    }

    Math.random = originalRandom;
});

test('Edge cases - negative outsCount should be handled gracefully', async () => {
    const originalRandom = Math.random;
    Math.random = () => 0; // Always buy

    const result = calculateInsuranceAmount(-5);

    // Should not throw error and should return valid result
    assert.ok(result === null || typeof result === 'number');

    Math.random = originalRandom;
});

test('Weighted random selection - should respect weight distribution', async () => {
    const originalRandom = Math.random;

    // Test that weighted selection works correctly by controlling random values
    const testCases = [
        { randomValue: 0.1, outsCount: LOW_OUTS_COUNT },
        { randomValue: 0.6, outsCount: LOW_OUTS_COUNT },
        { randomValue: 0.9, outsCount: LOW_OUTS_COUNT },
    ];

    for (const testCase of testCases) {
        Math.random = () => testCase.randomValue;
        const result = calculateInsuranceAmount(testCase.outsCount);

        if (result !== null) {
            // Note: Due to the weighted random algorithm, we can't predict exact values
            // but we can verify the result is within expected coverage amounts
            const validAmounts = [
                SERVER_COVERAGE_AMOUNT.LOW,
                SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                SERVER_COVERAGE_AMOUNT.FULL_POT,
            ];
            assert.ok(
                validAmounts.includes(result),
                `Coverage amount ${result} should be a valid coverage amount`,
            );
        }
    }

    Math.random = originalRandom;
});

test('Weighted random selection - fallback mechanism', async () => {
    const originalRandom = Math.random;

    // Test edge case where random threshold might not match any weight
    Math.random = () => 0.999999; // Very high random value

    const result = calculateInsuranceAmount(LOW_OUTS_COUNT);

    if (result !== null) {
        const validAmounts = [
            SERVER_COVERAGE_AMOUNT.LOW,
            SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
            SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
            SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
            SERVER_COVERAGE_AMOUNT.FULL_POT,
        ];
        assert.ok(
            validAmounts.includes(result),
            'Should return valid coverage amount even with edge case random value',
        );
    }

    Math.random = originalRandom;
});

test('Insurance probability validation - correct probabilities for each range', async () => {
    // This test validates that the probability logic matches the memory about opponent outs
    // Higher opponent outs should increase insurance buy probability

    const originalRandom = Math.random;

    // Test with a fixed random value that's between probabilities
    const testRandomValue = 0.3; // Between 0.25 and 0.35
    Math.random = () => testRandomValue;

    const testCases = [
        { outsCount: 1, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: LOW_OUTS_COUNT, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: 5, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: MEDIUM_OUTS_COUNT, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: 10, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: HIGH_OUTS_COUNT, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: 15, expectedBuy: true }, // 0.55 probability > 0.3
    ];

    for (const testCase of testCases) {
        const result = calculateInsuranceAmount(testCase.outsCount);
        const actualBuy = result !== null;
        assert.strictEqual(
            actualBuy,
            testCase.expectedBuy,
            `For outsCount ${testCase.outsCount}, expected buy decision to be ${testCase.expectedBuy} (higher outs should increase buy probability)`,
        );
    }

    Math.random = originalRandom;
});

test('Statistical validation - probability distribution over multiple runs', async () => {
    const originalRandom = Math.random;
    const runs = 1000;

    // Test LOW outsCount - should have ~10% buy rate
    Math.random = originalRandom; // Use real random
    let buyCount = 0;
    for (let i = 0; i < runs; i++) {
        const result = calculateInsuranceAmount(LOW_OUTS_COUNT);
        if (result !== null) buyCount++;
    }
    const buyRate = buyCount / runs;

    // Allow some variance but should be roughly around 0.1 (10%)
    assert.ok(
        buyRate >= 0.05 && buyRate <= 0.15,
        `Buy rate for LOW outsCount should be around 10%, got ${(buyRate * 100).toFixed(1)}%`,
    );

    // Test HIGH outsCount - should have higher buy rate
    buyCount = 0;
    for (let i = 0; i < runs; i++) {
        const result = calculateInsuranceAmount(15); // > HIGH threshold
        if (result !== null) buyCount++;
    }
    const highBuyRate = buyCount / runs;

    // Should be around 55% for very high outsCount
    assert.ok(
        highBuyRate >= 0.45 && highBuyRate <= 0.65,
        `Buy rate for very HIGH outsCount should be around 55%, got ${(highBuyRate * 100).toFixed(1)}%`,
    );

    // Verify that higher outsCount has higher buy probability
    assert.ok(highBuyRate > buyRate, 'Higher outsCount should result in higher insurance buy probability');
});

test('Comprehensive integration test - realistic poker scenarios', async () => {
    const originalRandom = Math.random;

    // Scenario 1: Opponent has very few outs (strong hand vs weak draw)
    Math.random = () => 0.05; // Below 0.1 threshold
    let result = calculateInsuranceAmount(2);
    assert.ok(result !== null, 'Should buy with low random value');

    Math.random = () => 0.15; // Above 0.1 threshold
    result = calculateInsuranceAmount(2);
    assert.strictEqual(result, null, 'Should not buy with higher random value');

    // Scenario 2: Opponent has many outs (vulnerable to multiple draws)
    Math.random = () => 0.5; // Below 0.55 threshold for high outs
    result = calculateInsuranceAmount(20);
    assert.ok(result !== null, 'Should buy insurance against many opponent outs');

    Math.random = () => 0.6; // Above 0.55 threshold
    result = calculateInsuranceAmount(20);
    assert.strictEqual(result, null, 'Should not buy with random above threshold');

    // Scenario 3: Medium risk scenario
    Math.random = () => 0.2; // Below 0.25 threshold for medium outs
    result = calculateInsuranceAmount(MEDIUM_OUTS_COUNT);
    assert.ok(result !== null, 'Should buy with medium outs and favorable random');

    Math.random = originalRandom;
});

test('selectCoverageAmount - Very HIGH outsCount should prefer higher coverage amounts', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(15); // outsCount > HIGH
        if (result !== null) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
        SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
        SERVER_COVERAGE_AMOUNT.FULL_POT,
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for very HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});

// Tests for weightedRandomSelection function from shared/src/strategy
test('weightedRandomSelection - basic functionality', async () => {
    const originalRandom = Math.random;

    const items = [
        { name: 'A', weight: 10 },
        { name: 'B', weight: 20 },
        { name: 'C', weight: 30 },
    ];

    // Test selection with controlled random values
    Math.random = () => 0.1; // Should select first item (weight 10, cumulative 10/60 = 0.167)
    let result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select item A with low random value');

    Math.random = () => 0.3; // Should select second item (weight 20, cumulative 30/60 = 0.5)
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should select item B with medium random value');

    Math.random = () => 0.8; // Should select third item (weight 30, cumulative 60/60 = 1.0)
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'C', 'Should select item C with high random value');

    Math.random = originalRandom;
});

test('weightedRandomSelection - edge cases', async () => {
    const originalRandom = Math.random;

    // Test with zero total weight
    const zeroWeightItems = [
        { name: 'A', weight: 0 },
        { name: 'B', weight: 0 },
    ];
    Math.random = () => 0.5;
    let result = weightedRandomSelection(zeroWeightItems, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should return first item when total weight is zero');

    // Test with single item
    const singleItem = [{ name: 'Only', weight: 100 }];
    result = weightedRandomSelection(singleItem, (item) => item.weight);
    assert.strictEqual(result.name, 'Only', 'Should return the only item');

    // Test with very high random value (edge of fallback)
    const items = [
        { name: 'A', weight: 10 },
        { name: 'B', weight: 20 },
    ];
    Math.random = () => 0.999999; // Very close to 1
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should return last item with very high random value');

    Math.random = originalRandom;
});

test('weightedRandomSelection - statistical distribution', async () => {
    const originalRandom = Math.random;
    Math.random = originalRandom; // Use real random

    const items = [
        { name: 'Low', weight: 10 }, // 10/60 = ~16.7%
        { name: 'Medium', weight: 20 }, // 20/60 = ~33.3%
        { name: 'High', weight: 30 }, // 30/60 = ~50%
    ];

    const counts = { Low: 0, Medium: 0, High: 0 };
    const runs = 3000;

    for (let i = 0; i < runs; i++) {
        const result = weightedRandomSelection(items, (item) => item.weight);
        counts[result.name as keyof typeof counts]++;
    }

    const lowRate = counts.Low / runs;
    const mediumRate = counts.Medium / runs;
    const highRate = counts.High / runs;

    // Allow some variance but should be roughly proportional to weights
    assert.ok(
        lowRate >= 0.12 && lowRate <= 0.22,
        `Low weight item should be selected ~16.7% of time, got ${(lowRate * 100).toFixed(1)}%`,
    );
    assert.ok(
        mediumRate >= 0.28 && mediumRate <= 0.38,
        `Medium weight item should be selected ~33.3% of time, got ${(mediumRate * 100).toFixed(1)}%`,
    );
    assert.ok(
        highRate >= 0.45 && highRate <= 0.55,
        `High weight item should be selected ~50% of time, got ${(highRate * 100).toFixed(1)}%`,
    );

    // Verify ordering: high > medium > low
    assert.ok(
        highRate > mediumRate && mediumRate > lowRate,
        'Selection rates should be proportional to weights',
    );
});

test('weightedRandomSelection - boundary conditions', async () => {
    const originalRandom = Math.random;

    const items = [
        { name: 'A', weight: 1 },
        { name: 'B', weight: 1 },
        { name: 'C', weight: 1 },
    ];

    // Test exact boundary values
    Math.random = () => 0; // Minimum random value
    let result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select first item with random = 0');

    Math.random = () => 1 / 3; // Exact boundary between A and B
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select first item at exact boundary');

    Math.random = () => 2 / 3; // Exact boundary between B and C
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should select second item at exact boundary');

    Math.random = () => 0.999; // Just below 1
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'C', 'Should select last item with high random value');

    Math.random = originalRandom;
});
